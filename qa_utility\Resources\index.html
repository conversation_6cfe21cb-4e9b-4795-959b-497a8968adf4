<!DOCTYPE html>
<html lang="tr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>QA Yardımcı Program Paneli</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
    <link href="https://ai-public.creatie.ai/gen_page/tailwind-custom.css" rel="stylesheet" />
    <script src="https://cdn.tailwindcss.com/3.4.5?plugins=forms@0.5.7,typography@0.5.13,aspect-ratio@0.4.2,container-queries@0.1.1"></script>
    <script src="https://ai-public.creatie.ai/gen_page/tailwind-config.min.js" data-color="#FF0000"></script>    <link href="styles.css" rel="stylesheet" />
    <script src="contextMenuHelper.js"></script>
    <script src="scripts.js"></script>
    <script src="testrail.js"></script>
  </head>
  <body class="bg-gray-900 text-gray-100 min-h-screen" style="font-size: small;">
    <!-- Modern Layout Container -->
    <div class="app-layout">
      <!-- Yeni Bildirim Banner -->
      <div id="notification-banner" class="hidden"></div>

      <!-- Oyun Seçimi Uyarı Modalı -->
      <div id="game-select-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-gray-800 p-6 shadow-lg max-w-md w-full rounded-lg">
          <div class="text-xl font-semibold mb-4">Uyarı</div>
          <p class="mb-6">Lütfen önce bir oyun seçin!</p>
          <div class="flex justify-end">
            <button id="game-select-modal-close" class="bg-custom hover:bg-custom/90 text-white px-4 py-2 rounded">Tamam</button>
          </div>
        </div>
      </div>

      <!-- Modern Header with Sidebar Toggle -->
      <header class="app-header">
        <div class="header-content">
          <!-- Left Section: Logo & Title -->
          <div class="header-left">
            <div class="app-logo">
              <i class="fas fa-bug text-custom text-xl"></i>
              <span class="app-title">QA Utility</span>
            </div>
          </div>

          <!-- Center Section: Controls -->
          <div class="header-center">
            <div class="control-group">
              <select id="os-select" class="modern-select">
                <option value="AOS">Android</option>
                <option value="IOS">iOS</option>
              </select>

              <div class="game-selector">
                <select id="gameSelect" class="modern-select game-select"></select>
                <button onclick="refreshGameList()" class="refresh-btn">
                  <i class="fas fa-sync-alt"></i>
                </button>
              </div>

              <div class="checkbox-container">
                <input type="checkbox" id="always-top" class="form-checkbox" />
                <label for="always-top">Her Zaman Üstte</label>
              </div>
            </div>
          </div>

          <!-- Right Section: User Info & Settings -->
          <div class="header-right">
            <div class="user-info">
              <span class="status-indicator online"></span>
              <span class="user-name">Kullanıcı</span>
            </div>
          </div>
        </div>

        <!-- Status Table Section -->
        <div class="status-section">
          <div class="status-table-container">
            <table class="status-table" id="studio-qa-table">
              <tbody>
                <tr class="border-b border-gray-600"></tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Modern Tab Navigation -->
        <nav class="tab-navigation">
          <div class="tab-container">
            <button data-tab="actions" onclick="openTab('actions')" class="tab-button tab-active">
              <i class="fas fa-cogs"></i>
              <span>İşlemler</span>
            </button>
            <button data-tab="qa-task" onclick="openTab('qa-task')" class="tab-button tab-inactive">
              <i class="fas fa-tasks"></i>
              <span>QA Task</span>
            </button>
            <button data-tab="video-ss" onclick="openTab('video-ss')" class="tab-button tab-inactive">
              <i class="fas fa-video"></i>
              <span>Video ve SS</span>
            </button>
            <button data-tab="payments" onclick="openTab('payments')" class="tab-button tab-inactive">
              <i class="fas fa-credit-card"></i>
              <span>Ödemeler</span>
            </button>
            <button data-tab="game-data" onclick="openTab('game-data')" class="tab-button tab-inactive">
              <i class="fas fa-gamepad"></i>
              <span>Oyun Verileri</span>
            </button>
            <button data-tab="local-logs" onclick="openTab('local-logs')" class="tab-button tab-inactive">
              <i class="fas fa-file-alt"></i>
              <span>Yerel Loglar</span>
            </button>
            <button data-tab="studioqa" onclick="openTab('studioqa')" class="tab-button tab-inactive">
              <i class="fas fa-plus-circle"></i>
              <span>Task Oluşturucu</span>
            </button>
            <button data-tab="testrail" onclick="openTab('testrail')" class="tab-button tab-inactive">
              <i class="fas fa-vial"></i>
              <span>TestRail</span>
            </button>
            <button data-tab="icard" onclick="openTab('icard')" class="tab-button tab-inactive">
              <i class="fas fa-id-card"></i>
              <span>iCard</span>
            </button>
            <button data-tab="admin-side" onclick="openTab('admin-side')" class="tab-button tab-inactive hidden">
              <i class="fas fa-user-shield"></i>
              <span>Yönetici Paneli</span>
            </button>
          </div>
        </nav>
      </header>

      <!-- Main Content Area -->
      <main class="app-main">
        <div class="content-area">
          <!-- iCard Integration -->

          <!-- Profil Sayfası -->
          <section id="profile" class="tab-content hidden scrollable-content p-8" style="height: calc(100vh - 200px);">
            <div class="max-w-6xl mx-auto space-y-8">

            <!-- Kullanıcı Bilgileri Kartı -->
            <div class="card card-elevated">
              <div class="card-header">
                <h2 class="card-title flex items-center gap-3 text-xl">
                  <i class="fas fa-user text-primary text-lg"></i>
                  Kişisel Bilgiler
                </h2>
              </div>
              <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label class="block text-sm font-medium text-secondary mb-3">Kullanıcı Adı</label>
                    <div class="p-4 bg-surface border border-border rounded-lg text-primary font-medium text-lg" id="profileUsername">-</div>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-secondary mb-3">E-posta Adresi</label>
                    <div class="p-4 bg-surface border border-border rounded-lg text-primary" id="profileEmail">-</div>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-secondary mb-3">IP Adresi</label>
                    <div class="p-4 bg-surface border border-border rounded-lg text-primary font-mono" id="profileIpAddress">-</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Sistem Bilgileri Kartı -->
            <div class="card card-elevated">
              <div class="card-header">
                <h2 class="card-title flex items-center gap-3 text-xl">
                  <i class="fas fa-cogs text-primary text-lg"></i>
                  Sistem Bilgileri
                </h2>
              </div>
              <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label class="block text-sm font-medium text-secondary mb-3">Kullanıcı Rolü</label>
                    <div class="p-4 bg-surface border border-border rounded-lg">
                      <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-primary text-white" id="profileRole">-</span>
                    </div>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-secondary mb-3">Son Giriş Tarihi</label>
                    <div class="p-4 bg-surface border border-border rounded-lg text-primary" id="profileLastLogin">-</div>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-secondary mb-3">Uygulama Versiyonu</label>
                    <div class="p-4 bg-surface border border-border rounded-lg">
                      <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-success text-white" id="profileAppVersion">v1.0.0</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </section>

          <section id="icard" class="tab-content hidden scrollable-content" style="height: calc(100vh - 200px);">
            <div class="w-full h-full flex flex-col items-center justify-center bg-gradient-to-br from-gray-900 to-gray-800 py-4 px-1 sm:py-8 sm:px-2">
            <div class="w-full max-w-6xl flex flex-col md:flex-row gap-4 md:gap-8 items-stretch justify-center">
              <!-- Login Card -->
              <div class="w-full max-w-md md:w-[340px] flex-shrink-0 flex flex-col items-center mb-4 md:mb-0">
                <div class="bg-gray-950/95 border border-custom/40 rounded-2xl shadow-2xl p-4 sm:p-8 w-full flex flex-col items-center">
                  <div class="bg-custom/20 rounded-full p-3 mb-3">
                    <i class="fas fa-id-card text-xl sm:text-2xl text-custom"></i>
                  </div>
                  <h2 class="text-xl sm:text-2xl font-bold text-gray-100 mb-4 sm:mb-6 tracking-tight">iCard Giriş</h2>
                  <form id="icardLoginForm" class="w-full space-y-3 sm:space-y-2">
                    <div>
                      <input type="text" id="icardUsername" autocomplete="username" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 sm:px-4 sm:py-2 text-gray-100 focus:outline-none focus:ring-2 focus:ring-custom text-base sm:text-lg" placeholder="Kullanıcı Adı">
                    </div>
                    <div>
                      <input type="password" id="icardPassword" autocomplete="current-password" class="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 sm:px-4 sm:py-2 text-gray-100 focus:outline-none focus:ring-2 focus:ring-custom text-base sm:text-lg" placeholder="Şifre">
                    </div>
                    <button type="button" id="icardLoginBtn" class="bg-custom hover:bg-custom/90 px-4 py-2 rounded-lg text-white w-full font-semibold transition text-base sm:text-lg">Giriş Yap</button>
                  </form>
                </div>
              </div>
              <!-- API Details Card -->
              <div class="flex-1 flex flex-col items-center">
                <div class="relative w-full h-full flex flex-col items-center">
                  <div class="bg-gray-950/95 border border-custom/40 rounded-2xl shadow-2xl p-4 sm:p-8 w-full flex flex-col items-center min-h-[320px] sm:min-h-[380px]">
                    <div class="flex flex-col sm:flex-row items-center mb-4 sm:mb-6 w-full gap-2 sm:gap-0">
                      <i class="fas fa-database text-lg sm:text-xl text-custom mr-0 sm:mr-3"></i>
                      <h2 class="text-lg sm:text-2xl font-bold text-gray-100 tracking-tight">iCard API Detayları</h2>
                      <button id="icardLogoutBtn" onclick="icardLogout()" class="sm:ml-auto mt-2 sm:mt-0 bg-gray-800 hover:bg-gray-700 border border-gray-700 text-gray-300 px-4 py-2 rounded-lg font-semibold transition hidden text-base sm:text-lg"><i class="fas fa-sign-out-alt mr-2"></i>Çıkış Yap</button>
                    </div>
                    <div id="icardApiDetails" class="bg-gray-900 rounded-xl p-4 sm:p-6 w-full flex flex-col items-center justify-center text-gray-400 min-h-[180px] sm:min-h-[220px] transition-all overflow-x-auto">
                      <div class="w-full flex flex-col items-center justify-center">
                        <div class="bg-gray-900 border-2 border-dashed border-custom/40 rounded-xl p-4 sm:p-8 flex flex-col items-center w-full max-w-lg min-h-[80px] sm:min-h-[120px]">
                          <span class="text-gray-400 text-base sm:text-lg flex items-center gap-2"><i class="fas fa-lock text-custom"></i> Henüz giriş yapılmadı.</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section id="login" class="tab-content hidden h-full flex items-center justify-center">
            <div class="flex min-h-screen w-full">
            <div class="flex-1 flex flex-col justify-center px-4 sm:px-6 lg:px-20 xl:px-24">
              <div class="mx-auto w-full max-w-sm">
                <div class="mb-8 text-center">
                  <div class="flex justify-center mb-4">
                    <div class="w-16 h-16 bg-custom rounded-full flex items-center justify-center shadow-lg shadow-custom/30">
                      <i class="fas fa-user-shield text-white text-2xl"></i>
                    </div>
                  </div>
                  <h2 class="text-3xl font-bold leading-9 tracking-tight text-white">QA Utility</h2>
                </div>
                <div class="space-y-6 mb-6">
                  <div>
                    <div class="relative">
                      <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <i class="fas fa-user text-gray-500"></i>
                      </div>
                      <input id="loginId" name="loginId" required placeholder="Kullanıcı ID'nizi girin" class="block w-full rounded-md border-0 bg-gray-800 py-2.5 pl-10 pr-3.5 text-gray-200 shadow-sm ring-1 ring-inset ring-gray-700 placeholder:text-gray-500 focus:ring-2 focus:ring-inset focus:ring-custom sm:text-sm sm:leading-6" />
                    </div>
                  </div>
                  <div>
                    <div class="relative">
                      <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <i class="fas fa-lock text-gray-500"></i>
                      </div>
                      <input id="loginPw" name="loginPw" type="password" autocomplete="current-password" required placeholder="Şifrenizi girin" class="block w-full rounded-md border-0 bg-gray-800 py-2.5 pl-10 pr-10 text-gray-200 shadow-sm ring-1 ring-inset ring-gray-700 placeholder:text-gray-500 focus:ring-2 focus:ring-inset focus:ring-custom sm:text-sm sm:leading-6" />
                    </div>
                  </div>
                </div>
                <div class="flex items-center justify-between mb-6">
                  <div class="checkbox-container">
                    <input id="remember-me" name="remember-me" type="checkbox" class="form-checkbox" />
                    <label for="remember-me" class="block text-sm leading-6 text-gray-300">Beni Hatırla</label>
                  </div>
                </div>
                <div>
                  <button onclick="handleLogin()" class="!rounded-lg flex w-full justify-center bg-custom px-3.5 py-3 text-sm font-semibold text-white hover:bg-custom/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-custom">
                    <i class="fas fa-sign-in-alt mr-2"></i> Giriş Yap
                  </button>
                </div>
              </div>
            </div>
          </section>

          <!-- Modern Actions Tab -->
          <section class="tab-content scrollable-content" id="actions" style="height: calc(100vh - 200px); overflow-y: auto;">
            <div class="modern-grid" style="margin-top: 10px; padding: var(--spacing-md);"">
            <!-- Log Sistemi Card -->
            <div id="log-system" class="card card-elevated">
              <div class="card-header">
                <h2 class="card-title flex items-center gap-2">
                  <i class="fas fa-clipboard-list text-primary"></i>
                  Log Sistemi
                </h2>
              </div>
              <div class="card-body space-y-2">
                <div>
                  <label for="logNote" class="block text-sm font-medium text-secondary mb-2">Log Notu:</label>
                  <textarea
                    id="logNote"
                    class="form-input resize-none"
                    style="min-height: 80px; max-height: 200px;"
                    placeholder="Log notunuzu buraya yazın...">Genel</textarea>
                </div>

                <button id="startLogRecordingButton" class="btn btn-primary w-full" onclick="startLogRecording()">
                  <i class="fas fa-play"></i>
                  Log Kaydını Başlat
                </button>

                <div class="text-center">
                  <div class="timer-display">
                    <div id="logTimer">00:00</div>
                    <div class="text-xs text-muted mt-1">Kayıt Süresi</div>
                  </div>
                </div>

                <button id="nvidiaRecordButton" class="btn btn-info w-full" onclick="recordLast30Seconds()">
                  <i class="fas fa-video"></i>
                  Son 30 Saniyeyi Kaydet
                </button>
              </div>
            </div>

            <!-- Cihaz Fonksiyonları Card -->
            <div id="device-functions" class="card card-elevated">
              <div class="card-header">
                <h2 class="card-title flex items-center gap-2">
                  <i class="fas fa-mobile-alt text-primary"></i>
                  Cihaz Fonksiyonları
                </h2>
              </div>
              <div class="card-body space-y-2">
                <!-- Storage Selection -->
                <div class="bg-surface-hover rounded-lg p-3">
                  <label for="storageSelect" class="block text-sm font-medium text-secondary mb-2">
                    <i class="fas fa-hdd mr-1"></i>
                    Kayıt Konumu
                  </label>
                  <div class="flex gap-2" style="display: flex; flex-direction: column;">
                    <select id="storageSelect" class="form-input flex-1">
                      <!-- Disk seçenekleri JavaScript ile doldurulacak -->
                    </select>
                    <div>
                      <button id="saveStorageLocation" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                      </button>
                      <button id="refreshStorageLocation" onclick="getStorageLocations()" class="btn btn-ghost" title="Konumları Yenile">
                        <i class="fas fa-sync-alt"></i>
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Device Controls -->
                <div class="device-controls-grid grid grid-cols-2 gap-2">
                  <button class="btn btn-warning" onclick="restartDevice()">
                    <i class="fas fa-power-off"></i>
                    Cihazı Yeniden Başlat
                  </button>
                  <button id="wirelessButton" class="btn btn-primary" onclick="switchToWireless()">
                    <i class="fas fa-wifi"></i>
                    Kablosuz Bağlantı
                  </button>
                </div>

                <!-- Video Recording Section -->
                <div class="bg-surface-hover rounded-lg p-3 space-y-3">
                  <div class="checkbox-container">
                    <input type="checkbox" id="videorecordloop" class="form-checkbox" />
                    <label for="videorecordloop" class="text-sm font-medium">Sürekli Kayıt</label>
                  </div>

                  <button class="btn btn-primary w-full" onclick="startVideoRecording()">
                    <i class="fas fa-video"></i>
                    Video Kaydı Başlat
                  </button>

                  <div class="flex items-center justify-center gap-2">
                    <div class="timer-display">
                      <div id="videoTimer">00:00</div>
                    </div>
                    <button class="btn btn-ghost btn-sm" id="videoPauseBtn" onclick="videoTimerPause()" title="Duraklat/Devam Et">
                      <i class="fas fa-play"></i>
                    </button>
                    <button class="btn btn-ghost btn-sm" id="videoResetBtn" onclick="videoTimerReset()" title="Sıfırla">
                      <i class="fas fa-undo"></i>
                    </button>
                  </div>
                </div>

                <button class="btn btn-secondary w-full" onclick="takeScreenshot()">
                  <i class="fas fa-camera"></i>
                  Ekran Görüntüsü Al
                </button>

                <!-- Device Info Section -->
                <div class="device-info-grid">
                  <div class="flex items-center justify-between mb-3">
                    <h3 class="font-semibold text-sm flex items-center gap-2">
                      <i class="fas fa-info-circle text-info"></i>
                      Cihaz Bilgileri
                    </h3>
                    <button class="btn btn-ghost btn-sm" onclick="copyDeviceInfo()" title="Bilgileri Kopyala">
                      <i class="fas fa-copy"></i>
                    </button>
                  </div>
                  <div id="device-info" class="space-y-1">
                    <div class="device-info-item">
                      <span class="device-info-label">Marka:</span>
                      <span id="device-brand" class="device-info-value">-</span>
                    </div>
                    <div class="device-info-item">
                      <span class="device-info-label">Model:</span>
                      <span id="device-model" class="device-info-value">-</span>
                    </div>
                    <div class="device-info-item">
                      <span class="device-info-label">Sürüm:</span>
                      <span id="device-version" class="device-info-value">-</span>
                    </div>
                    <div class="device-info-item">
                      <span class="device-info-label">Chipset:</span>
                      <span id="device-chipset" class="device-info-value">-</span>
                    </div>
                    <div class="device-info-item">
                      <span class="device-info-label">CPU:</span>
                      <span id="device-cpu" class="device-info-value">-</span>
                    </div>
                    <div class="device-info-item">
                      <span class="device-info-label">Çözünürlük:</span>
                      <span id="device-resolution" class="device-info-value">-</span>
                    </div>
                    <div class="device-info-item">
                      <span class="device-info-label">Dil:</span>
                      <span id="device-language" class="device-info-value">-</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Uygulama Fonksiyonları Card -->
            <div id="app-functions" class="card card-elevated">
              <div class="card-header">
                <h2 class="card-title flex items-center gap-2">
                  <i class="fas fa-mobile-alt text-primary"></i>
                  Uygulama Fonksiyonları
                </h2>
              </div>
              <div class="card-body space-y-2">
                <div class="action-button-group">
                  <button class="btn btn-primary" onclick="launchApp()">
                    <i class="fas fa-play"></i>
                    Başlat
                  </button>
                  <button class="btn btn-warning" onclick="closeApp()">
                    <i class="fas fa-stop"></i>
                    Kapat
                  </button>
                </div>

                <div class="action-button-group">
                  <button class="btn btn-info" onclick="openInternalLink()">
                    <i class="fas fa-link"></i>
                    Internal
                  </button>
                  <button class="btn btn-secondary" onclick="viewInStore()">
                    <i class="fas fa-store"></i>
                    Store
                  </button>
                </div>

                <div class="action-button-group">
                  <button class="btn btn-danger" onclick="clearAppData()">
                    <i class="fas fa-trash"></i>
                    Veri Sil
                  </button>
                  <button class="btn btn-danger" onclick="uninstallApp()">
                    <i class="fas fa-times"></i>
                    Kaldır
                  </button>
                </div>

                <button class="btn btn-primary w-full" onclick="uploadApk()">
                  <i class="fas fa-upload"></i>
                  APK Yükle
                </button>

                <!-- App Info Section -->
                <div class="device-info-grid">
                  <div class="flex items-center justify-between mb-3">
                    <h3 class="font-semibold text-sm flex items-center gap-2">
                      <i class="fas fa-info-circle text-info"></i>
                      Uygulama Bilgileri
                    </h3>
                    <button onclick="refreshAppInfo()" class="btn btn-ghost btn-sm" title="Bilgileri Yenile">
                      <i class="fas fa-sync-alt"></i>
                    </button>
                  </div>
                  <div class="space-y-1">
                    <div class="device-info-item">
                      <span class="device-info-label">Versiyon:</span>
                      <span id="app-version" class="device-info-value">-</span>
                    </div>
                    <div class="device-info-item">
                      <span class="device-info-label">Build:</span>
                      <span id="build-number" class="device-info-value">-</span>
                    </div>
                    <div class="device-info-item">
                      <span class="device-info-label">Bundle ID:</span>
                      <span id="bundle-id" class="device-info-value">-</span>
                    </div>
                    <div class="device-info-item">
                      <span class="device-info-label">Target API:</span>
                      <span id="target-api" class="device-info-value">-</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Admin Komutları Card -->
            <div id="admin-commands" class="card card-elevated">
              <div class="card-header">
                <h2 class="card-title flex items-center gap-2">
                  <i class="fas fa-terminal text-warning"></i>
                  Admin Komutları
                </h2>
              </div>
              <div class="card-body space-y-2">
                <!-- Admin Düğmeleri - Tek sıra alt alta -->
                <button class="btn btn-info w-full" onclick="turnOffWifi()">
                  <i class="fas fa-wifi"></i>
                  Wifi Kapat
                </button>
                <button class="btn btn-primary w-full" onclick="runMonkey()">
                  <i class="fas fa-bug"></i>
                  Run Monkey
                </button>
                <button class="btn btn-danger w-full" onclick="uninstallAllApps()">
                  <i class="fas fa-trash-alt"></i>
                  Tüm Uygulamaları Kaldır
                </button>
                <button class="btn btn-danger w-full" onclick="killAdbServer()">
                  <i class="fas fa-skull"></i>
                  Kill ADB Server
                </button>

                <!-- Metin Gönder Bölümü -->
                <div class="space-y-1">
                  <label class="block text-sm font-medium text-secondary">Metin Gönder:</label>
                  <textarea
                    id="sendTextArea"
                    class="form-input resize-none"
                    rows="3"
                    placeholder="Cihaza gönderilecek metni buraya yazın..."></textarea>
                  <button class="btn btn-primary w-full" onclick="sendText()">
                    <i class="fas fa-paper-plane"></i>
                    Metni Gönder
                  </button>
                </div>

                <!-- MediaNova Cache Temizle -->
                <button class="btn btn-warning w-full" onclick="purgeMediaNova()">
                  <i class="fas fa-broom"></i>
                  MediaNova Cache Temizle
                </button>
              </div>
            </div>
          </section>

          <!-- Modern Video-SS Tab -->
          <section id="video-ss" class="tab-content hidden scrollable-content p-4" style="height: calc(100vh - 200px)">
          <div class="card card-elevated h-full flex flex-col">
            <!-- Header -->
            <div class="card-header">
              <div class="flex items-center justify-between">
                <h2 class="card-title flex items-center gap-2">
                  <i class="fas fa-video text-primary"></i>
                  Video ve Ekran Görüntüleri
                </h2>
                <button id="refreshVideoSsButton" class="btn btn-ghost btn-sm" title="Listeyi Yenile">
                  <i class="fas fa-sync-alt"></i>
                </button>
              </div>

              <!-- Search Bar -->
              <div class="mt-3">
                <div class="search-input-container">
                  <i class="fas fa-search search-icon"></i>
                  <input
                    type="text"
                    id="videoSsSearchInput"
                    class="form-input"
                    placeholder="Dosya adı, oyun adı veya tarih ile ara..."
                  />
                </div>
              </div>
            </div>

            <!-- Table Container -->
            <div class="card-body flex-1 overflow-hidden p-0">
              <div class="table-responsive h-full">
                <div class="table-container">
                  <table id="videoSsTable" class="w-full">
                    <thead>
                      <tr>
                        <th data-sort="fileName">
                          <i class="fas fa-file mr-2"></i>
                          Dosya Adı
                          <i class="fas fa-sort text-muted ml-1"></i>
                        </th>
                        <th data-sort="gameName">
                          <i class="fas fa-gamepad mr-2"></i>
                          Oyun Adı
                          <i class="fas fa-sort text-muted ml-1"></i>
                        </th>
                        <th data-sort="date">
                          <i class="fas fa-calendar mr-2"></i>
                          Tarih
                          <i class="fas fa-sort text-muted ml-1"></i>
                        </th>
                        <th data-sort="type">
                          <i class="fas fa-tag mr-2"></i>
                          Tip
                          <i class="fas fa-sort text-muted ml-1"></i>
                        </th>
                        <th data-sort="status">
                          <i class="fas fa-info-circle mr-2"></i>
                          Durum
                          <i class="fas fa-sort text-muted ml-1"></i>
                        </th>
                      </tr>
                    </thead>
                    <tbody id="videoSsTableBody">
                      <!-- Veriler JavaScript ile doldurulacak -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <!-- Modern Context Menu -->
          <div id="videoSsContextMenu" class="context-menu hidden">
            <ul>
              <li id="contextOpen">
                <i class="fas fa-folder-open"></i>
                <span>Dosyayı Aç</span>
              </li>
              <li id="contextCopy">
                <i class="fas fa-copy"></i>
                <span>Kopyala</span>
              </li>
              <li class="separator"></li>
              <li id="contextUploadToDrive">
                <i class="fas fa-cloud-upload-alt text-info"></i>
                <span>Drive'a Yükle</span>
              </li>
              <li class="separator"></li>
              <li id="contextDelete">
                <i class="fas fa-trash-alt text-error"></i>
                <span>Sil</span>
              </li>
            </ul>
          </section>

          <!-- Modern QA Task Tab -->
          <section id="qa-task" class="tab-content hidden scrollable-content p-4" style="height: calc(100vh - 200px)">
          <div class="card card-elevated h-full flex flex-col">
            <!-- Header -->
            <div class="card-header">
              <div class="flex items-center justify-between">
                <h2 class="card-title flex items-center gap-2">
                  <i class="fas fa-tasks text-primary"></i>
                  QA Task Yönetimi
                </h2>
                <button id="refreshQaTaskButton" class="btn btn-ghost btn-sm" title="Listeyi Yenile">
                  <i class="fas fa-sync-alt"></i>
                  <span>Yenile</span>
                </button>
              </div>

              <!-- Search Bar -->
              <div class="mt-3">
                <div class="search-input-container">
                  <i class="fas fa-search search-icon"></i>
                  <input
                    type="text"
                    id="qaTaskSearch"
                    class="form-input"
                    placeholder="Task ID, konu veya proje ile ara..."
                  />
                </div>
              </div>
            </div>

            <!-- Table Container -->
            <div class="card-body flex-1 overflow-hidden p-0">
              <div class="table-responsive h-full overflow-x-auto">
                <div class="table-container">
                  <table id="qaTaskTable" class="w-full min-w-max table-fixed"">
                    <thead>
                      <tr>
                        <th data-sort="id" class="w-16 text-xs">
                          <i class="fas fa-hashtag mr-1"></i>
                          ID
                          <i class="fas fa-sort text-muted ml-1"></i>
                        </th>
                        <th data-sort="subject" class="w-64 text-xs">
                          <i class="fas fa-file-alt mr-1"></i>
                          Konu
                          <i class="fas fa-sort text-muted ml-1"></i>
                        </th>
                        <th data-sort="project" class="w-32 text-xs">
                          <i class="fas fa-folder mr-1"></i>
                          Proje
                          <i class="fas fa-sort text-muted ml-1"></i>
                        </th>
                        <th data-sort="status" class="w-24 text-xs">
                          <i class="fas fa-flag mr-1"></i>
                          Durum
                          <i class="fas fa-sort text-muted ml-1"></i>
                        </th>
                        <th data-sort="priority" class="w-24 text-xs">
                          <i class="fas fa-exclamation-triangle mr-1"></i>
                          Öncelik
                          <i class="fas fa-sort text-muted ml-1"></i>
                        </th>
                        <th data-sort="assignedTo" class="w-24 text-xs">
                          <i class="fas fa-user mr-1"></i>
                          Atanan
                          <i class="fas fa-sort text-muted ml-1"></i>
                        </th>
                        <th data-sort="startDate" class="w-28 text-xs">
                          <i class="fas fa-calendar-plus mr-1"></i>
                          Başlangıç
                          <i class="fas fa-sort text-muted ml-1"></i>
                        </th>
                        <th data-sort="dueDate" class="w-28 text-xs">
                          <i class="fas fa-calendar-times mr-1"></i>
                          Bitiş
                          <i class="fas fa-sort text-muted ml-1"></i>
                        </th>
                        <th data-sort="doneRatio" class="w-20 text-xs">
                          <i class="fas fa-percentage mr-1"></i>
                          %
                          <i class="fas fa-sort text-muted ml-1"></i>
                        </th>
                      </tr>
                    </thead>
                    <tbody id="qaTaskTableBody">
                      <!-- Veriler JavaScript ile doldurulacak -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <!-- Modern QA Task Context Menu -->
          <div id="qaTaskContextMenu" class="context-menu hidden">
            <ul>
              <li id="qaTaskContextEdit">
                <i class="fas fa-edit"></i>
                <span>Düzenle</span>
              </li>
              <li id="qaTaskContextComment">
                <i class="fas fa-comment"></i>
                <span>Yorum Ekle</span>
              </li>
              <li class="separator"></li>
              <li class="relative group">
                <div class="flex items-center justify-between">
                  <span>Durumu Değiştir</span>
                  <i class="fas fa-chevron-right ml-2"></i>
                </div>
                <ul class="absolute left-full top-0 hidden group-hover:block bg-gray-800 rounded shadow-md min-w-[150px]" id="qaTaskStatusSubmenu">
                  <!-- Statuses will be loaded dynamically -->
                </ul>
              </li>
              <li class="separator"></li>
              <li id="qaTaskContextOpenRedmine">
                <i class="fas fa-external-link-alt text-info"></i>
                <span>Redmine'da Aç</span>
              </li>
              <li id="qaTaskContextRefresh">
                <i class="fas fa-sync-alt"></i>
                <span>Tüm Listeyi Yenile</span>
              </li>
            </ul>
          </section>

          <section id="payments" class="tab-content hidden scrollable-content">
          <div class="bg-gray-800 p-1 w-full flex gap-4 h-full max-h-[calc(100vh-165px)]">
            <!-- Sol Bölüm (%10) -->
            <div class="w-[20%]">
              <div class="bg-gray-700 p-1" style="height: 100%;display: flex;flex-direction: column;">
                <h2 class="text-lg font-semibold text-center mb-2">Ödeme Kayıtları</h2>
                <div class="text-sm text-gray-300 mb-2 text-center" id="selectedGameName">-</div>
                <textarea
                  id="paymentDetails"
                  style="height: 100%"
                  class="w-full h-48 bg-gray-600 border border-gray-500 rounded p-2 text-sm text-gray-100 mb-3 resize-none"
                  placeholder="Ödeme detaylarını girin..."></textarea>
                <button
                  onclick="savePayments()"
                  class="w-full bg-custom hover:bg-custom/90 text-white py-2 px-4 rounded transition-colors">
                  Ödemeleri Kaydet
                </button>
              </div>
            </div>

            <!-- Sağ Bölüm (%90) -->
            <div class="w-[80%]">
              <!-- Üst Kontroller -->
              <div class="bg-gray-700 p-1">
                <div class="flex items-center justify-between gap-4">                  <div class="flex items-center space-x-4">
                    <div class="checkbox-container">
                      <input
                        type="checkbox"
                        checked ="true"
                        id="showPending"
                        class="form-checkbox"
                      />
                      <label for="showPending" class="text-gray-300">Bekleyenleri Listele</label>
                    </div>
                    <div class="checkbox-container">
                      <input
                        type="checkbox"
                        id="autoRefund"
                        class="form-checkbox"
                      />
                      <label for="autoRefund" class="text-gray-300">Otomatik Refund</label>
                    </div>
                  </div>
                  <div class="flex-1 max-w-2xl">
                    <input
                      type="text"
                      id="paymentSearch"
                      class="w-full bg-gray-600 border border-gray-500 rounded px-4 py-2 text-gray-100"
                      placeholder="Kayıtlarda ara..."
                    />
                  </div>
                  <button
                    onclick="refreshPayments()"
                    class="bg-gray-600 hover:bg-gray-500 p-2 rounded transition-colors">
                    <i class="fas fa-sync-alt text-gray-300"></i>
                  </button>
                </div>
              </div>

              <!-- Tablo -->
              <div class="bg-gray-700 overflow-hidden">
                <table id="paymentsTable" class="w-full text-left">
                  <thead class="bg-gray-800">
                    <tr>
                      <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="id">
                        ID <i class="fas fa-sort text-gray-500 ml-1"></i>
                      </th>
                      <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="appName">
                        Uygulama Adı <i class="fas fa-sort text-gray-500 ml-1"></i>
                      </th>
                      <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="tester">
                        Testçi <i class="fas fa-sort text-gray-500 ml-1"></i>
                      </th>
                      <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="bundleId">
                        Bundle ID <i class="fas fa-sort text-gray-500 ml-1"></i>
                      </th>
                      <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="paymentPin">
                        Ödeme PIN <i class="fas fa-sort text-gray-500 ml-1"></i>
                      </th>
                      <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="status">
                        Durum <i class="fas fa-sort text-gray-500 ml-1"></i>
                      </th>
                    </tr>
                  </thead>
                  <tbody class="divide-y divide-gray-600">
                    <!-- Veriler JavaScript ile doldurulacak -->
                  </tbody>
                </table>
              </div>
            </div>
            <!-- Payments tablosu için context menu -->
            <div id="paymentsContextMenu" class="hidden absolute z-50 bg-gray-800 text-gray-200 rounded shadow-md">
                <ul class="list-none p-1 m-0">
                    <li id="paymentcontextDelete" class="px-4 py-2 hover:bg-gray-700 cursor-pointer">Sil</li>
                    <li class="relative group">
                        <div class="px-4 py-2 hover:bg-gray-700 cursor-pointer flex items-center justify-between">
                            <span>Durumu Değiştir</span>
                            <i class="fas fa-chevron-right ml-2"></i>
                        </div>
                        <ul class="absolute left-full top-0 hidden group-hover:block bg-gray-800 rounded shadow-md min-w-[150px]">
                            <li id="contextStatusPending" class="px-4 py-2 hover:bg-gray-700 cursor-pointer">Bekliyor</li>
                            <li id="contextStatusCompleted" class="px-4 py-2 hover:bg-gray-700 cursor-pointer">Tamamlandı</li>
                        </ul>
                    </li>
                    <li id="contextRefund" class="px-4 py-2 hover:bg-gray-700 cursor-pointer">Geri Öde</li>
                </ul>
            </div>
          </section>

          <section id="admin-side" class="tab-content hidden scrollable-content">
          <div class="bg-gray-800 w-full">
            <div class="w-full">
              <div class="mb-1 border-b border-gray-700">
                <ul class="flex flex-wrap -mb-px text-sm text-center">
                  <li class="mr-2">
                    <a href="#" data-admin-tab="user-data" onclick="switchAdminTab('user-data')" class="admin-tab inline-block p-2 hover:text-custom hover:border-custom text-custom border-b-2 border-custom">Kullanıcı Verileri</a>
                  </li>
                  <li class="mr-2">
                    <a href="#" data-admin-tab="version" onclick="switchAdminTab('version')" class="admin-tab inline-block p-2 hover:text-custom hover:border-custom">Sürüm</a>
                  </li>
                  <li class="mr-2">
                    <a href="#" data-admin-tab="studio-qa" onclick="switchAdminTab('studio-qa')" class="admin-tab inline-block p-2 hover:text-custom hover:border-custom">Stüdyo QA</a>
                  </li>
                  <li class="mr-2">
                    <a href="#" data-admin-tab="dataviewer" onclick="switchAdminTab('dataviewer')" class="admin-tab inline-block p-2 hover:text-custom hover:border-custom">Veri Görüntüleyici</a>
                  </li>

                  <li class="mr-2">
                    <a href="#" data-admin-tab="all-tasks" onclick="switchAdminTab('all-tasks')" class="admin-tab inline-block p-2 hover:text-custom hover:border-custom">All Tasks</a>
                  </li>
                  <li class="mr-2">
                    <a href="#" data-admin-tab="file-hasher" onclick="switchAdminTab('file-hasher')" class="admin-tab inline-block p-2 hover:text-custom hover:border-custom">Dosya Hashleyici</a>
                  </li>
                </ul>
              </div>
              <!-- User Data Tab Content -->
              <div id="admin-user-data" class="admin-tab-content p-4">
                <div class="flex gap-4 mb-4">
                  <div class="flex-1">
                    <input type="text" id="userDataSearch" placeholder="Kullanıcı adı veya rol ile ara..." class="w-full bg-gray-700 border-gray-600 !rounded-button text-gray-100 px-4 py-2" />
                  </div>
                  <button id="addUserButton" class="bg-green-600 hover:bg-green-700 p-2 rounded transition-colors" title="Yeni Kullanıcı Ekle">
                    <i class="fas fa-plus text-white"></i>
                  </button>
                  <button id="refreshUserDataButton" class="bg-gray-700 hover:bg-gray-600 p-2 rounded transition-colors" title="Yenile">
                    <i class="fas fa-sync-alt text-gray-300"></i>
                  </button>
                </div>
                <div class="bg-gray-700 p-4 overflow-auto max-h-[calc(100vh-250px)]">
                  <table id="userDataTable" class="w-full text-left">
                    <thead class="bg-gray-800">
                      <tr>
                        <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="id">
                          ID <i class="fas fa-sort text-gray-500 ml-1"></i>
                        </th>
                        <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="user">
                          Kullanıcı Adı <i class="fas fa-sort text-gray-500 ml-1"></i>
                        </th>
                        <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="role">
                          Rol <i class="fas fa-sort text-gray-500 ml-1"></i>
                        </th>
                        <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="lastlogin">
                          Son Giriş <i class="fas fa-sort text-gray-500 ml-1"></i>
                        </th>
                        <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="ip">
                          IP <i class="fas fa-sort text-gray-500 ml-1"></i>
                        </th>
                        <th class="px-4 py-3 text-right">İşlemler</th>
                      </tr>
                    </thead>
                    <tbody id="userDataTableBody" class="divide-y divide-gray-600">
                      <!-- Veriler JavaScript ile doldurulacak -->
                    </tbody>
                  </table>
                </div>
              </div>
              <!-- Version Tab Content -->
              <div id="admin-version" class="admin-tab-content hidden p-4">
                <div class="flex gap-4 mb-4">
                  <div class="flex-1">
                    <input type="text" id="versionSearch" placeholder="Uygulama adı ile ara..." class="w-full bg-gray-700 border-gray-600 !rounded-button text-gray-100 px-4 py-2" />
                  </div>
                  <button id="refreshVersionButton" class="bg-gray-700 hover:bg-gray-600 p-2 rounded transition-colors" title="Yenile">
                    <i class="fas fa-sync-alt text-gray-300"></i>
                  </button>
                </div>
                <div class="bg-gray-700 p-4 overflow-auto max-h-[calc(100vh-250px)]">
                  <table id="versionTable" class="w-full text-left">
                    <thead class="bg-gray-800">
                      <tr>
                        <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="app">
                          Uygulama <i class="fas fa-sort text-gray-500 ml-1"></i>
                        </th>
                        <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="version">
                          Sürüm <i class="fas fa-sort text-gray-500 ml-1"></i>
                        </th>
                      </tr>
                    </thead>
                    <tbody id="versionTableBody" class="divide-y divide-gray-600">
                      <!-- Veriler JavaScript ile doldurulacak -->
                    </tbody>
                  </table>
                </div>
              </div>
              <!-- Studio QA Tab Content -->
              <div id="admin-studio-qa" class="admin-tab-content hidden p-4">
                <div class="flex gap-4 mb-4">
                  <div class="flex-1">
                    <input type="text" id="studioQASearch" placeholder="Tester adı ile ara..." class="w-full bg-gray-700 border-gray-600 !rounded-button text-gray-100 px-4 py-2" />
                  </div>
                  <button id="refreshStudioQAButton" class="bg-gray-700 hover:bg-gray-600 p-2 rounded transition-colors" title="Yenile">
                    <i class="fas fa-sync-alt text-gray-300"></i>
                  </button>
                  <button id="addStudioQAButton" class="bg-green-600 hover:bg-green-700 p-2 rounded transition-colors" title="Yeni Kayıt Ekle">
                    <i class="fas fa-plus text-white"></i>
                  </button>
                </div>
                <div class="bg-gray-700 p-4 overflow-auto max-h-[calc(100vh-250px)]">
                  <table id="studioQATable" class="w-full text-left">
                    <thead class="bg-gray-800">
                      <tr>
                        <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="id">
                          ID <i class="fas fa-sort text-gray-500 ml-1"></i>
                        </th>
                        <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="tester">
                          Tester <i class="fas fa-sort text-gray-500 ml-1"></i>
                        </th>
                        <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="priority1">
                          Priority 1 <i class="fas fa-sort text-gray-500 ml-1"></i>
                        </th>
                        <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="priority2">
                          Priority 2 <i class="fas fa-sort text-gray-500 ml-1"></i>
                        </th>
                        <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="priority3">
                          Priority 3 <i class="fas fa-sort text-gray-500 ml-1"></i>
                        </th>
                        <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="priority4">
                          Priority 4 <i class="fas fa-sort text-gray-500 ml-1"></i>
                        </th>
                        <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="priority5">
                          Priority 5 <i class="fas fa-sort text-gray-500 ml-1"></i>
                        </th>
                        <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="priority6">
                          Priority 6 <i class="fas fa-sort text-gray-500 ml-1"></i>
                        </th>
                        <th class="px-4 py-3 text-right">İşlemler</th>
                      </tr>
                    </thead>
                    <tbody id="studioQATableBody" class="divide-y divide-gray-600">
                      <!-- Veriler JavaScript ile doldurulacak -->
                    </tbody>
                  </table>
                </div>
              </div>
              <!-- Data Viewer Tab Content -->
              <div id="admin-dataviewer" class="admin-tab-content hidden p-4">
                <div class="flex gap-4 mb-4">
                  <div class="flex-1">
                    <input type="text" id="dataViewerSearch" placeholder="Dosya adı ile ara..." class="w-full bg-gray-700 border-gray-600 !rounded-button text-gray-100 px-4 py-2" />
                  </div>
                  <button id="refreshDataViewerButton" class="bg-gray-700 hover:bg-gray-600 p-2 rounded transition-colors" title="Yenile">
                    <i class="fas fa-sync-alt text-gray-300"></i>
                  </button>
                </div>
                <div class="bg-gray-700 p-4 overflow-auto max-h-[calc(100vh-250px)]">
                  <table id="dataViewerTable" class="w-full text-left">
                    <thead class="bg-gray-800">
                      <tr>
                        <th class="px-4 py-3">
                          <input type="checkbox" id="dataViewerSelectAll" class="form-checkbox">
                        </th>
                        <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="testerName">
                          Tester <i class="fas fa-sort text-gray-500 ml-1"></i>
                        </th>
                        <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="gameName">
                          Oyun <i class="fas fa-sort text-gray-500 ml-1"></i>
                        </th>
                        <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="date">
                          Tarih <i class="fas fa-sort text-gray-500 ml-1"></i>
                        </th>
                        <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="version">
                          Versiyon <i class="fas fa-sort text-gray-500 ml-1"></i>
                        </th>
                        <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="os">
                          OS <i class="fas fa-sort text-gray-500 ml-1"></i>
                        </th>
                        <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="status">
                          Status <i class="fas fa-sort text-gray-500 ml-1"></i>
                        </th>
                        <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="size">
                          Boyut <i class="fas fa-sort text-gray-500 ml-1"></i>
                        </th>
                      </tr>
                    </thead>
                    <tbody id="dataViewerTableBody" class="divide-y divide-gray-600">
                      <!-- Veriler JavaScript ile doldurulacak -->
                    </tbody>
                  </table>
                </div>
              </div>

              <!-- Data Viewer Context Menu -->
              <div id="dataViewerContextMenu" class="hidden absolute z-50 bg-gray-800 text-gray-200 rounded shadow-md">
                <ul class="list-none p-1 m-0">
                  <li id="dataViewerContextOpen" class="px-4 py-2 hover:bg-gray-700 cursor-pointer">
                    <i class="fas fa-external-link-alt mr-2"></i> Aç
                  </li>
                  <li id="dataViewerContextArchive" class="px-4 py-2 hover:bg-gray-700 cursor-pointer">
                    <i class="fas fa-archive mr-2"></i> Arşive Aktar
                  </li>
                </ul>
              </div>
              
              
              <div id="admin-all-tasks" class="admin-tab-content hidden p-4">
                <div class="flex gap-4 mb-4">
                  <div class="flex-1">
                    <input type="text" id="allTasksSearch" placeholder="Task ara..." class="w-full bg-gray-700 border-gray-600 !rounded-button text-gray-100 px-4 py-2" />
                  </div>
                  <button id="refreshAllTasksButton" class="bg-custom hover:bg-custom/90 !rounded-button px-4 py-2 flex items-center">
                    <i class="fas fa-sync-alt mr-2"></i>Yenile
                  </button>
                </div>
<div class="bg-gray-700 p-4 overflow-auto max-h-[calc(100vh-250px)] overflow-x-auto">
                  <table id="allTasksTable" class="w-full text-left">
                    <thead class="bg-gray-800">
                      <tr>
                        <th class="p-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="id">ID <i class="fas fa-sort text-gray-500 ml-1"></i></th>
                        <th class="p-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="subject">Konu <i class="fas fa-sort text-gray-500 ml-1"></i></th>
                        <th class="p-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="project">Proje <i class="fas fa-sort text-gray-500 ml-1"></i></th>
                        <th class="p-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="status">Durum <i class="fas fa-sort text-gray-500 ml-1"></i></th>
                        <th class="p-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="priority">Öncelik <i class="fas fa-sort text-gray-500 ml-1"></i></th>
                        <th class="p-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="assignedTo">Atanan <i class="fas fa-sort text-gray-500 ml-1"></i></th>
                        <th class="p-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="startDate">Başlangıç Tarihi <i class="fas fa-sort text-gray-500 ml-1"></i></th>
                        <th class="p-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="dueDate">Bitiş Tarihi <i class="fas fa-sort text-gray-500 ml-1"></i></th>
                        <th class="p-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="doneRatio">Tamamlanma Oranı <i class="fas fa-sort text-gray-500 ml-1"></i></th>
                      </tr>
                    </thead>
                    <tbody id="allTasksTableBody" class="divide-y divide-gray-600">
                      <!-- Veriler JavaScript ile doldurulacak -->
                    </tbody>
                  </table>
                </div>
              </div>
              <!-- File Hasher Tab Content -->
              <div id="admin-file-hasher" class="admin-tab-content hidden" style="height: calc(100vh - 200px);">
                <div class="flex gap-4 mb-1">
                  <button id="getHashesBtn" onclick="getFileHashes()" class="bg-custom hover:bg-custom/90 !rounded-button px-1 py-2 flex-1">
                    <i class="fas fa-database mr-2"></i>Verileri Getir
                  </button>
                  <button id="checkFilesBtn" onclick="checkFiles()" class="bg-custom hover:bg-custom/90 !rounded-button px-1 py-2 flex-1">
                    <i class="fas fa-check-circle mr-2"></i>Dosyaları Kontrol Et
                  </button>
                  <button id="saveHashesBtn" onclick="saveHashes()" class="bg-custom hover:bg-custom/90 !rounded-button px-1 py-2 flex-1">
                    <i class="fas fa-save mr-2"></i>Verileri Kaydet
                  </button>
                  <button id="resetTableBtn" onclick="resetHashTable()" class="bg-custom hover:bg-custom/90 !rounded-button px-1 py-2 flex-1">
                    <i class="fas fa-trash mr-2"></i>Tüm Tabloyu Sıfırla
                  </button>
                </div>
                <div class="flex gap-4 mb-4" style="display:none">
                  <div class="flex-1">
                    <input type="text" id="fileHashSearch" placeholder="Dosya adı veya hash ile ara..." class="w-full bg-gray-700 border-gray-600 !rounded-button text-gray-100 px-4 py-2" />
                  </div>
                  <div class="checkbox-container">
                    <input type="checkbox" id="showOnlyChangedFiles" class="form-checkbox" />
                    <label for="showOnlyChangedFiles" class="text-gray-300">Sadece Değişenleri Göster</label>
                  </div>
                </div>
                <div class="bg-gray-700 p-1 overflow-auto overflow-x-auto">
                  <table id="fileHashTable" class="w-full text-left">
                    <thead class="bg-gray-800">
                      <tr>
                        <th class="p-1 cursor-pointer hover:bg-gray-700" data-sort="id">
                          <div class="flex items-center">
                            <span>ID</span>
                            <i class="fas fa-sort ml-2 text-gray-500 sort-icon"></i>
                          </div>
                        </th>
                        <th class="p-1 cursor-pointer hover:bg-gray-700" data-sort="filehash">
                          <div class="flex items-center">
                            <span>Dosya Hash</span>
                            <i class="fas fa-sort ml-2 text-gray-500 sort-icon"></i>
                          </div>
                        </th>
                        <th class="p-1 cursor-pointer hover:bg-gray-700" data-sort="path">
                          <div class="flex items-center">
                            <span>Yol</span>
                            <i class="fas fa-sort ml-2 text-gray-500 sort-icon"></i>
                          </div>
                        </th>
                        <th class="p-1 cursor-pointer hover:bg-gray-700" data-sort="filename">
                          <div class="flex items-center">
                            <span>Dosya Adı</span>
                            <i class="fas fa-sort ml-2 text-gray-500 sort-icon"></i>
                          </div>
                        </th>
                        <th class="p-1 cursor-pointer hover:bg-gray-700" data-sort="status">
                          <div class="flex items-center">
                            <span>Durum</span>
                            <i class="fas fa-sort ml-2 text-gray-500 sort-icon"></i>
                          </div>
                        </th>
                      </tr>
                    </thead>
                    <tbody id="fileHashTableBody">
                      <!-- Veriler JavaScript ile doldurulacak -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <!-- All Tasks Context Menu -->
            <div id="allTasksContextMenu" class="hidden absolute z-50 bg-gray-800 text-gray-200 rounded shadow-md">
              <ul class="list-none p-1 m-0">
                <li id="allTasksContextEdit" class="px-4 py-2 hover:bg-gray-700 cursor-pointer">
                  <i class="fas fa-edit mr-2"></i> Düzenle
                </li>
                <li id="allTasksContextComment" class="px-4 py-2 hover:bg-gray-700 cursor-pointer">
                  <i class="fas fa-comment mr-2"></i> Yorum Ekle
                </li>
                <li class="relative group">
                  <div class="px-4 py-2 hover:bg-gray-700 cursor-pointer flex items-center justify-between">
                    <span>Durumu Değiştir</span>
                    <i class="fas fa-chevron-right ml-2"></i>
                  </div>
                  <ul class="absolute left-full top-0 hidden group-hover:block bg-gray-800 rounded shadow-md min-w-[150px]" id="allTasksStatusSubmenu">
                    <!-- Statuses will be loaded dynamically -->
                  </ul>
                </li>
                <li id="allTasksContextOpenRedmine" class="px-4 py-2 hover:bg-gray-700 cursor-pointer">
                  <i class="fas fa-external-link-alt mr-2"></i> Redmine'da Aç
                </li>
                <li id="allTasksContextRefresh" class="px-4 py-2 hover:bg-gray-700 cursor-pointer">
                  <i class="fas fa-sync-alt mr-2"></i> Tüm Listeyi Yenile
                </li>
              </ul>
            </div>
          </div>
        </main>
        <main id="game-data" class="tab-content hidden scrollable-content">
          <div class="bg-gray-700 p-1 overflow-auto h-[calc(100vh-165px)]">
              <!-- Üst Kontroller -->
              <div class="flex justify-between items-center mb-1">
                <div class="flex-1 mr-4">
                  <input type="text" id="gamesDataSearch" placeholder="Oyun adı, bundle ID veya yayıncı ile ara..." class="w-full bg-gray-700 border-gray-600 !rounded-button text-gray-100 px-4 py-2" />
                </div>
                <div class="flex space-x-2">
                  <button id="addGameButton" class="bg-green-600 hover:bg-green-700 p-2 rounded transition-colors" title="Yeni Oyun Ekle">
                    <i class="fas fa-plus text-white"></i>
                  </button>
                  <button id="refreshGamesDataButton" class="bg-gray-700 hover:bg-gray-600 p-2 rounded transition-colors" title="Yenile">
                    <i class="fas fa-sync-alt text-gray-300"></i>
                  </button>
                </div>
              </div>

              <!-- Tablo -->
              <div class="bg-gray-700 overflow-x-auto" style="height: calc(100vh - 220px);">
                <table id="gamesDataTable" class="w-full text-left whitespace-nowrap">
                  <thead class="bg-gray-800">
                    <tr>
                      <!-- Her zaman görünür sütunlar -->
                      <th class="px-2 py-2 cursor-pointer hover:bg-gray-700 transition-colors text-xs md:text-sm" data-sort="name">
                        Oyun Adı <i class="fas fa-sort text-gray-500 ml-1"></i>
                      </th>
                      <th class="px-2 py-2 cursor-pointer hover:bg-gray-700 transition-colors text-xs md:text-sm" data-sort="bundleid">
                        Bundle ID (AOS) <i class="fas fa-sort text-gray-500 ml-1"></i>
                      </th>
                      <th class="px-2 py-2 cursor-pointer hover:bg-gray-700 transition-colors text-xs md:text-sm" data-sort="publisher">
                        Yayıncı <i class="fas fa-sort text-gray-500 ml-1"></i>
                      </th>
                      <th class="px-2 py-2 cursor-pointer hover:bg-gray-700 transition-colors text-xs md:text-sm" data-sort="status">
                        Durum <i class="fas fa-sort text-gray-500 ml-1"></i>
                      </th>

                      <!-- Küçük ekranlarda gizlenen sütunlar -->
                      <th class="px-2 py-2 cursor-pointer hover:bg-gray-700 transition-colors text-xs md:text-sm hidden md:table-cell" data-sort="bundleidios">
                        Bundle ID (iOS) <i class="fas fa-sort text-gray-500 ml-1"></i>
                      </th>
                      <th class="px-2 py-2 cursor-pointer hover:bg-gray-700 transition-colors text-xs md:text-sm hidden md:table-cell" data-sort="iosappname">
                        iOS App Adı <i class="fas fa-sort text-gray-500 ml-1"></i>
                      </th>
                      <th class="px-2 py-2 cursor-pointer hover:bg-gray-700 transition-colors text-xs md:text-sm hidden md:table-cell" data-sort="aosinternal">
                        AOS Internal <i class="fas fa-sort text-gray-500 ml-1"></i>
                      </th>
                      <th class="px-2 py-2 cursor-pointer hover:bg-gray-700 transition-colors text-xs md:text-sm hidden md:table-cell" data-sort="tfurl">
                        TF URL <i class="fas fa-sort text-gray-500 ml-1"></i>
                      </th>
                    </tr>
                  </thead>
                  <tbody id="gamesDataTableBody" class="divide-y divide-gray-600 text-xs md:text-sm">
                    <!-- Veriler JavaScript ile doldurulacak -->
                  </tbody>
                </table>
              </div>

              <!-- Context Menu -->
              <div id="gamesDataContextMenu" class="hidden absolute z-50 bg-gray-800 text-gray-200 rounded shadow-md">
                <ul class="list-none p-1 m-0">
                  <li id="gamesDataContextCopy" class="px-4 py-2 hover:bg-gray-700 cursor-pointer">
                    <i class="fas fa-copy mr-2"></i> Bundle ID'yi Kopyala
                  </li>
                  <li id="gamesDataContextOpenStore" class="px-4 py-2 hover:bg-gray-700 cursor-pointer">
                    <i class="fas fa-external-link-alt mr-2"></i> Store'da Göster
                  </li>
                  <li id="gamesDataContextEdit" class="px-4 py-2 hover:bg-gray-700 cursor-pointer">
                    <i class="fas fa-edit mr-2"></i> Düzenle
                  </li>
                  <li id="gamesDataContextDelete" class="px-4 py-2 hover:bg-gray-700 cursor-pointer text-red-400">
                    <i class="fas fa-trash-alt mr-2"></i> Sil
                  </li>
                </ul>
              </div>
          </div>
        </main>
        <main id="local-logs" class="tab-content hidden scrollable-content">
          <div class="bg-gray-800 w-full h-full max-h-[calc(100vh-165px)]">
            <!-- Üst Kontroller -->
            <div class="bg-gray-700 p-1 mb-1">
              <div class="flex items-center justify-between gap-4">
                <div class="flex-1 max-w-2xl">
                  <input
                    type="text"
                    id="localLogsSearchInput"
                    class="w-full bg-gray-600 border border-gray-500 rounded px-4 py-2 text-gray-100"
                    placeholder="Log dosyalarında ara..."
                  />
                </div>
                <button
                  id="refreshLocalLogsButton"
                  class="bg-gray-600 hover:bg-gray-500 p-2 rounded transition-colors">
                  <i class="fas fa-sync-alt text-gray-300"></i>
                </button>
              </div>
            </div>

            <!-- Tablo -->
            <div class="bg-gray-700 overflow-hidden">
              <table id="localLogsTable" class="w-full text-left">
                <thead class="bg-gray-800">
                  <tr>
                    <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="fileName">
                      Dosya Adı <i class="fas fa-sort text-gray-500 ml-1"></i>
                    </th>
                    <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="date">
                      Tarih <i class="fas fa-sort text-gray-500 ml-1"></i>
                    </th>
                    <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="size">
                      Boyut <i class="fas fa-sort text-gray-500 ml-1"></i>
                    </th>
                    <th class="px-4 py-3 cursor-pointer hover:bg-gray-700 transition-colors text-right">
                      İşlemler
                    </th>
                  </tr>
                </thead>
                <tbody id="localLogsTableBody" class="divide-y divide-gray-600">
                  <!-- JavaScript ile doldurulacak -->
                </tbody>
              </table>
            </div>

            <!-- Context Menu -->
            <div id="localLogsContextMenu" style="z-index: 9999;" class="hidden absolute z-50 bg-gray-800 text-gray-200 rounded shadow-md">
                <ul class="list-none p-1 m-0">
                    <li id="localLogsContextOpen" class="px-4 py-2 hover:bg-gray-700 cursor-pointer">
                        <i class="fas fa-external-link-alt mr-2"></i> Aç
                    </li>
                    <li id="localLogsContextOpenLocation" class="px-4 py-2 hover:bg-gray-700 cursor-pointer">
                        <i class="fas fa-folder-open mr-2"></i> Dosya Konumunu Aç
                    </li>
                    <li id="localLogsContextUploadToFtp" class="px-4 py-2 hover:bg-gray-700 cursor-pointer">
                        <i class="fas fa-upload mr-2"></i> FTP'ye Tekrar Yükle
                    </li>
                    <li id="localLogsContextDelete" class="px-4 py-2 hover:bg-gray-700 cursor-pointer text-red-400">
                        <i class="fas fa-trash-alt mr-2"></i> Sil
                    </li>
                </ul>
            </div>
          </div>
        </main>
        <!-- TestRail Integration -->
        <main id="testrail" class="tab-content hidden scrollable-content" style="height: calc(100vh - 135px)">
          <div class="bg-gray-800 w-full h-full flex gap-4 p-4">
            <!-- Left Panel - Project Selector -->
            <div class="w-1/3 bg-gray-700 p-4 rounded">
              <div class="mb-4">
                <h2 class="text-lg font-semibold text-gray-100 mb-4">
                  <i class="fas fa-project-diagram mr-2"></i>TestRail Projects
                </h2>
                
                <!-- Connection Status -->
                <div id="testRailConnectionStatus" class="mb-4 p-3 rounded bg-gray-600">
                  <div class="flex items-center">
                    <div id="testRailStatusIcon" class="w-3 h-3 rounded-full bg-gray-400 mr-2"></div>
                    <span id="testRailStatusText" class="text-sm text-gray-300">Checking connection...</span>
                  </div>
                </div>

                <!-- Project Selector -->
                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-300 mb-2">Select Project:</label>
                  <select id="testRailProjectSelect" class="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-gray-100 disabled:opacity-50" disabled>
                    <option value="">Loading projects...</option>
                  </select>
                </div>                <!-- Suite Selector -->
                <div class="mb-4" id="testRailSuiteContainer" style="display: none;">
                  <label class="block text-sm font-medium text-gray-300 mb-2">Test Suite:</label>
                  <select id="testRailSuiteSelect" class="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-gray-100">
                    <option value="">All Suites</option>
                  </select>
                </div>

                <!-- Project Info -->
                <div id="testRailProjectInfo" class="bg-gray-600 p-3 rounded text-sm text-gray-300" style="display: none;">
                  <h3 class="font-medium mb-2">Project Information</h3>
                  <div id="testRailProjectDetails"></div>
                </div>
              </div>
            </div>

            <!-- Right Panel - Test Runs -->
            <div class="flex-1 bg-gray-700 p-4 rounded">
              <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-100">
                  <i class="fas fa-running mr-2"></i>Test Runs
                </h2>
                
                <div class="flex gap-2">
                  <!-- Search -->
                  <div class="relative">
                    <input type="text" id="testRailSearch" placeholder="Search test runs..." 
                           class="bg-gray-600 border border-gray-500 rounded px-3 py-2 text-gray-100 pl-8" />
                    <i class="fas fa-search absolute left-2 top-3 text-gray-400"></i>
                  </div>
                  
                  <!-- Refresh Button -->
                  <button id="testRailRefreshBtn" class="bg-custom hover:bg-custom/90 px-3 py-2 rounded transition-colors" disabled>
                    <i class="fas fa-sync-alt"></i>
                  </button>
                </div>
              </div>

              <!-- Test Runs Table -->
              <div class="bg-gray-600 rounded overflow-hidden" style="height: calc(100% - 80px);">
                <div class="overflow-auto h-full">
                  <table id="testRailRunsTable" class="w-full text-left">
                    <thead class="bg-gray-800 sticky top-0">
                      <tr>
                        <th class="px-4 py-3 text-gray-100 font-medium">ID</th>
                        <th class="px-4 py-3 text-gray-100 font-medium">Name</th>
                        <th class="px-4 py-3 text-gray-100 font-medium">Created On</th>
                        <th class="px-4 py-3 text-gray-100 font-medium">Completed</th>
                        <th class="px-4 py-3 text-gray-100 font-medium">Status</th>
                        <th class="px-4 py-3 text-gray-100 font-medium">Actions</th>
                      </tr>
                    </thead>
                    <tbody id="testRailRunsTableBody" class="divide-y divide-gray-500">
                      <tr>
                        <td colspan="6" class="px-4 py-8 text-center text-gray-400">
                          <i class="fas fa-info-circle mr-2"></i>
                          Select a project to view test runs
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </main>
        <!-- Task Creator sekmesi -->
        <main id="studioqa" class="tab-content scrollable-content hidden">
          <div class="bg-gray-800 p-4 w-full h-full max-h-[calc(100vh-165px)]">
            <div class="max-w-6xl mx-auto grid grid-cols-2 gap-6 mb-6">
              <!-- Studio QA Task Creator Group -->
              <div class="bg-gray-700 p-4">
                <h2 class="text-xl font-semibold mb-4 text-gray-100">Studio QA Task Creator</h2>
                <button onclick="createStudioQATask()" class="bg-custom hover:bg-custom/90 !rounded-button px-6 py-3 text-white font-medium">
                  Create Task
                </button>
              </div>

              <!-- Live Test Task Creator Group -->
              <div class="bg-gray-700 p-4">
                <h2 class="text-xl font-semibold mb-4 text-gray-100">Live Test Task Creator</h2>
                <button onclick="createLiveTestTask()" class="bg-custom hover:bg-custom/90 !rounded-button px-6 py-3 text-white font-medium">
                  Create Task
                </button>
              </div>
            </div>

            <!-- Studio QA Tasks DataTable -->
            <div class="max-w-6xl mx-auto">
              <div class="bg-gray-700 p-4">
                <div class="flex justify-between items-center mb-4">
                  <h2 class="text-xl font-semibold text-gray-100">Studio QA Tasks</h2>
                  <div class="flex gap-4">
                    <!-- <div class="flex-1">
                      <input type="text" id="studioQATasksSearch" placeholder="Task ara..." class="w-full bg-gray-600 border-gray-600 !rounded-button text-gray-100 px-4 py-2" />
                    </div> -->
                    <button id="refreshStudioQATasksButton" class="bg-custom hover:bg-custom/90 !rounded-button px-4 py-2 flex items-center">
                      <i class="fas fa-sync-alt mr-2"></i>Yenile
                    </button>
                  </div>
                </div>
                <div class="bg-gray-600 p-4 overflow-auto max-h-[calc(100vh-350px)] overflow-x-auto">
                  <table id="studioQATasksTable" class="w-full text-left">
                    <thead class="bg-gray-800">
                      <tr>
                        <th class="p-3 cursor-pointer hover:bg-gray-700 transition-colors hidden" data-sort="id">ID <i class="fas fa-sort text-gray-500 ml-1"></i></th>
                        <th class="p-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="customField">Oyun Adı <i class="fas fa-sort text-gray-500 ml-1"></i></th>
                        <th class="p-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="author">Oluşturan <i class="fas fa-sort text-gray-500 ml-1"></i></th>
                        <th class="p-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="project">Proje <i class="fas fa-sort text-gray-500 ml-1"></i></th>
                        <th class="p-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="status">Durum <i class="fas fa-sort text-gray-500 ml-1"></i></th>
                        <th class="p-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="startDate">Başlangıç Tarihi <i class="fas fa-sort text-gray-500 ml-1"></i></th>
                        <th class="p-3 cursor-pointer hover:bg-gray-700 transition-colors" data-sort="subject">Konu <i class="fas fa-sort text-gray-500 ml-1"></i></th>
                      </tr>
                    </thead>
                    <tbody id="studioQATasksTableBody" class="divide-y divide-gray-600">
                      <!-- Veriler JavaScript ile doldurulacak -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>      
      <!-- Task Creator Modal -->
      <div id="taskModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-gray-800 p-6 w-96">
          <h3 id="modalTitle" class="text-xl font-semibold mb-4 text-gray-100">Task Oluştur</h3>
          <form id="taskModalForm">
            <div class="space-y-2">
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Oyun</label>
                <input type="text" id="modalGameName" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">İşletim Sistemi</label>
                <input type="text" id="modalOS" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Versiyon</label>
                <input type="text" id="modalVersion" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Build Numarası</label>
                <input type="text" id="modalBuildNumber" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
              </div>
            </div>
            <div class="flex justify-end gap-3 mt-6">
              <button type="button" onclick="closeTaskModal()" class="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded">
                İptal
              </button>
              <button type="button" onclick="submitTaskModal()" class="bg-custom hover:bg-custom/90 text-white px-4 py-2 rounded">
                Tamam
              </button>
            </div>
          </form>
        </div>
      </div>
      <!-- Confirmation Modal -->
      <div id="confirmationModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden">
          <div class="bg-gray-800 p-6 shadow-xl">
              <p id="confirmationMessage" class="text-white mb-4"></p>
              <div class="flex justify-end space-x-4">
                  <button id="cancelButton" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                      İptal
                  </button>
                  <button id="confirmButton" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
                      Tamam
                  </button>
              </div>
          </div>
      </div>
      <!-- Studio QA Modal -->
      <div id="studioQAModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-gray-800 p-6 w-[600px]">
          <h3 id="studioQAModalTitle" class="text-xl font-semibold mb-4 text-gray-100">Yeni Studio QA Kaydı Ekle</h3>
          <form id="studioQAModalForm">
            <div class="grid grid-cols-2 gap-4">
              <div class="col-span-2">
                <label class="block text-sm font-medium text-gray-300 mb-1">Tester</label>
                <input type="text" id="studioQAModalTester" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
                <input type="hidden" id="studioQAModalId" value="">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Priority 1</label>
                <input type="text" id="studioQAModalPriority1" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Priority 2</label>
                <input type="text" id="studioQAModalPriority2" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Priority 3</label>
                <input type="text" id="studioQAModalPriority3" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Priority 4</label>
                <input type="text" id="studioQAModalPriority4" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Priority 5</label>
                <input type="text" id="studioQAModalPriority5" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Priority 6</label>
                <input type="text" id="studioQAModalPriority6" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
              </div>
            </div>
            <div class="flex justify-end gap-3 mt-6">
              <button type="button" id="studioQAModalCancel" class="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded">
                İptal
              </button>
              <button type="button" id="studioQAModalSave" class="bg-custom hover:bg-custom/90 text-white px-4 py-2 rounded">
                Kaydet
              </button>
            </div>
          </form>
        </div>
      </div>
      <!-- Studio QA Delete Confirmation Modal -->
      <div id="deleteStudioQAModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-gray-800 p-6 w-96">
          <h3 class="text-xl font-semibold mb-4 text-gray-100">Studio QA Kaydını Sil</h3>
          <p class="mb-6 text-gray-300">Bu kaydı silmek istediğinizden emin misiniz?</p>
          <input type="hidden" id="deleteStudioQAId" value="" />
          <div class="flex justify-end gap-3">
            <button type="button" id="deleteStudioQACancel" class="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded">
              İptal
            </button>
            <button type="button" id="deleteStudioQAConfirm" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded">
              Sil
            </button>
          </div>
        </div>
      </div>
      <!-- Google Drive Folder Selection Modal -->
      <div id="driveFolderModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
          <div class="bg-gray-800 p-6 shadow-xl w-3/4 max-w-3xl">
              <div class="flex justify-between items-center mb-4">
                  <h3 class="text-xl text-white font-semibold">Google Drive Klasör Seçimi</h3>
                  <button id="driveModalClose" class="text-gray-400 hover:text-white">
                      <i class="fas fa-times"></i>
                  </button>
              </div>

              <!-- Search Box -->
              <div class="mb-4">
                  <div class="relative">
                      <input type="text" id="driveFolderSearch" placeholder="Klasör ara..."
                            class="w-full bg-gray-700 border border-gray-600 rounded text-gray-100 px-4 py-2 pl-10" />
                      <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                          <i class="fas fa-search text-gray-400"></i>
                      </div>
                  </div>
              </div>

              <!-- Folder Tree Container -->
              <div class="bg-gray-700 p-2 mb-4 h-64 overflow-auto">
                  <ul id="driveFolderTree" class="text-gray-200">
                      <!-- Folders will be loaded here -->
                      <li class="flex items-center py-1 px-2">
                          <i class="fas fa-spinner fa-spin mr-2"></i>
                          <span>Klasörler yükleniyor...</span>
                      </li>
                  </ul>
              </div>

              <!-- New Folder Creation -->
              <div class="flex mb-4">
                  <input type="text" id="newFolderName" placeholder="Yeni klasör adı" class="flex-grow bg-gray-700 border border-gray-600 rounded-l text-gray-100 px-4 py-2" />
              </div>

              <!-- Action Buttons -->
              <div class="flex justify-end space-x-4">
                  <button id="driveCancelBtn" class="px-4 py-2 bg-gray-700 text-white rounded hover:bg-gray-600">İptal</button>
                  <button id="driveSelectBtn" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-500" disabled>Seç</button>
              </div>
          </div>
      </div>
      <!-- QA Task Edit Modal -->
      <div id="qaTaskEditModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-gray-800 p-6 w-[600px] max-h-[80vh] overflow-y-auto">
          <h3 class="text-xl font-semibold mb-4 text-gray-100">Task Düzenle</h3>
          <form id="qaTaskEditForm">
            <input type="hidden" id="qaTaskEditId">
            <div class="grid grid-cols-2 gap-4 mb-4">
              <div class="col-span-2">
                <label class="block text-sm font-medium text-gray-300 mb-1">Konu</label>
                <input type="text" id="qaTaskEditSubject" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Durum</label>
                <select id="qaTaskEditStatus" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
                  <!-- Options will be loaded dynamically -->
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Öncelik</label>
                <select id="qaTaskEditPriority" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
                  <!-- Options will be loaded dynamically -->
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Atanan</label>
                <select id="qaTaskEditAssignedTo" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
                  <!-- Options will be loaded dynamically -->
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Tamamlanma Oranı</label>
                <select id="qaTaskEditDoneRatio" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
                  <option value="0">0%</option>
                  <option value="10">10%</option>
                  <option value="20">20%</option>
                  <option value="30">30%</option>
                  <option value="40">40%</option>
                  <option value="50">50%</option>
                  <option value="60">60%</option>
                  <option value="70">70%</option>
                  <option value="80">80%</option>
                  <option value="90">90%</option>
                  <option value="100">100%</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Başlangıç Tarihi</label>
                <input type="date" id="qaTaskEditStartDate" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Bitiş Tarihi</label>
                <input type="date" id="qaTaskEditDueDate" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
              </div>
              <div class="col-span-2">
                <label class="block text-sm font-medium text-gray-300 mb-1">Açıklama</label>
                <textarea id="qaTaskEditDescription" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2 min-h-[100px]"></textarea>
              </div>
            </div>
            <div class="flex justify-end gap-3 mt-6">
              <button type="button" id="qaTaskEditCancel" class="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded">
                İptal
              </button>
              <button type="button" id="qaTaskEditSave" class="bg-custom hover:bg-custom/90 text-white px-4 py-2 rounded">
                Kaydet
              </button>
            </div>
          </form>
        </div>
      </div>
      <!-- QA Task Comment Modal -->
      <div id="qaTaskCommentModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-gray-800 p-6 w-[500px]">
          <h3 class="text-xl font-semibold mb-4 text-gray-100">Yorum Ekle</h3>
          <form id="qaTaskCommentForm">
            <input type="hidden" id="qaTaskCommentId">
            <div class="mb-4">
              <textarea id="qaTaskCommentText" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2 min-h-[150px]" placeholder="Yorumunuzu buraya yazın..."></textarea>
            </div>
            <div class="flex justify-end gap-3">
              <button type="button" id="qaTaskCommentCancel" class="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded">
                İptal
              </button>
              <button type="button" id="qaTaskCommentSave" class="bg-custom hover:bg-custom/90 text-white px-4 py-2 rounded">
                Gönder
              </button>
            </div>
          </form>
        </div>
      </div>
      <!-- Oyun Düzenleme/Ekleme Modal -->
      <div id="gameEditModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-gray-800 p-6 w-[600px] max-h-[90vh] overflow-y-auto">
          <h3 id="gameModalTitle" class="text-xl font-semibold mb-4 text-gray-100">Oyun Düzenle</h3>
          <form id="gameModalForm">
            <div class="space-y-2">
              <input type="hidden" id="gameModalId" value="">
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Oyun Adı</label>
                <input type="text" id="gameModalName" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Bundle ID (AOS)</label>
                <input type="text" id="gameModalBundleId" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Bundle ID (iOS)</label>
                <input type="text" id="gameModalBundleIdIos" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">iOS App Adı</label>
                <input type="text" id="gameModalIosAppName" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">AOS Internal</label>
                <input type="text" id="gameModalAosInternal" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">TF URL</label>
                <input type="text" id="gameModalTfUrl" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Yayıncı</label>
                <input type="text" id="gameModalPublisher" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Durum</label>
                <select id="gameModalStatus" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
                  <option value="Aktif">Aktif</option>
                  <option value="Pasif">Pasif</option>
                  <option value="pending">Beklemede</option>
                </select>
              </div>
            </div>
            <div class="mt-6 flex justify-end space-x-3">
              <button type="button" onclick="closeGameModal()" class="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded">
                İptal
              </button>
              <button type="button" onclick="submitGameModal()" class="bg-custom hover:bg-custom/90 text-white px-4 py-2 rounded">
                Kaydet
              </button>
            </div>
          </form>
        </div>
      </div>
      <!-- Kullanıcı Düzenleme/Ekleme Modal -->
      <div id="userModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-gray-800 p-6 w-96">
          <h3 id="userModalTitle" class="text-xl font-semibold mb-4 text-gray-100">Kullanıcı Ekle</h3>
          <form id="userModalForm">
            <div class="space-y-2">
              <input type="hidden" id="userModalId" value="" />
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Kullanıcı Adı</label>
                <input type="text" id="userModalUsername" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1">Rol</label>
                <select id="userModalRole" class="w-full bg-gray-700 border-gray-600 rounded text-gray-100 px-3 py-2">
                  <option value="admin">Admin</option>
                  <option value="tester">Tester</option>
                </select>
              </div>
            </div>
            <div class="flex justify-end gap-3 mt-6">
              <button type="button" id="userModalCancel" class="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded">
                İptal
              </button>
              <button type="button" id="userModalSave" class="bg-custom hover:bg-custom/90 text-white px-4 py-2 rounded">
                Kaydet
              </button>
            </div>
          </form>
        </div>
      </div>
      <!-- Kullanıcı Silme Onay Modal -->
      <div id="deleteUserModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-gray-800 p-6 w-96">
          <h3 class="text-xl font-semibold mb-4 text-gray-100">Kullanıcı Sil</h3>
          <p class="mb-6 text-gray-300">Bu kullanıcıyı silmek istediğinizden emin misiniz?</p>
          <input type="hidden" id="deleteUserId" value="" />
          <div class="flex justify-end gap-3">
            <button type="button" id="deleteUserCancel" class="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded">
              İptal
            </button>
            <button type="button" id="deleteUserConfirm" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded">
              Sil
            </button>
          </div>
        </div>
      </div>
        </div>
      </main>

      <!-- Modern Footer -->
      <footer class="app-footer">
        <div class="footer-content">
          <div class="footer-left">
            <span id="version-info" class="footer-item">
              <i class="fas fa-code-branch"></i>
              Sürüm: 2.75
            </span>
            <span id="status-info" class="footer-item status-ready">
              <i class="fas fa-check-circle"></i>
              Durum: Hazır
            </span>
            <span id="device-status" class="footer-item status-disconnected">
              <i class="fas fa-mobile-alt"></i>
              Cihaz: Bağlı Değil (USB)
            </span>
          </div>
          <div class="footer-right">
            <span id="loginUserName" class="footer-user" onclick="openTab('profile')">
              <i class="fas fa-user"></i>
              <span>Kullanıcı</span>
            </span>
            <button class="logout-btn" onclick="closeApplication()">
              <i class="fas fa-sign-out-alt"></i>
              Oturumu Kapat
            </button>
          </div>
        </div>
      </footer>
    </div>
  </body>
</html>